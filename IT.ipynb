'''
import sys
import time
import threading
import os
import csv
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem,
    QComboBox, QLabel, QPushButton, QFileDialog
)
from PyQt5.QtCore import QTimer, Qt
from ping3 import ping

ALIAS_FILE = "device_aliases.csv"

def generate_default_aliases():
    if not os.path.exists(ALIAS_FILE):
        with open(ALIAS_FILE, "w", newline="") as f:
            writer = csv.writer(f)
            writer.writerow(["IP", "Name"])
            for i in range(1, 255):
                writer.writerow([f"192.168.0.{i}", ""])

def load_aliases():
    aliases = {}
    with open(ALIAS_FILE, "r") as f:
        reader = csv.DictReader(f)
        for row in reader:
            ip = row["IP"].strip()
            name = row["Name"].strip()
            aliases[ip] = name if name else ip
    return aliases

def save_aliases(aliases):
    with open(ALIAS_FILE, "w", newline="") as f:
        writer = csv.writer(f)
        writer.writerow(["IP", "Name"])
        for ip, name in aliases.items():
            writer.writerow([ip, name])

generate_default_aliases()
ALIASES = load_aliases()
IP_RANGE = list(ALIASES.keys())
systems = {ip: {"name": ALIASES[ip], "latency": None, "last_checked": 0} for ip in IP_RANGE}

class PingMonitor(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Network Ping Monitor with Filters and Export")

        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["All", "Active", "Offline"])
        self.filter_combo.currentTextChanged.connect(self.update_ui)

        self.export_btn = QPushButton("Export Filtered to CSV")
        self.export_btn.clicked.connect(self.export_filtered)

        self.table = QTableWidget(0, 4)
        self.table.setHorizontalHeaderLabels(["IP", "Name", "Latency (ms)", "Status"])
        self.table.itemChanged.connect(self.handle_item_change)

        layout = QVBoxLayout()
        layout.addWidget(QLabel("Show:"))
        layout.addWidget(self.filter_combo)
        layout.addWidget(self.table)
        layout.addWidget(self.export_btn)
        self.setLayout(layout)

        threading.Thread(target=self.ping_loop, daemon=True).start()

        self.timer = QTimer()
        self.timer.timeout.connect(self.update_ui)
        self.timer.start(1000)

        self._suppress_item_change = False

    def ping_loop(self):
        while True:
            for ip in systems:
                try:
                    latency = ping(ip, timeout=0.5)
                    systems[ip]["latency"] = round(latency * 1000, 2) if latency else None
                    systems[ip]["last_checked"] = time.time()
                except Exception:
                    systems[ip]["latency"] = None
                time.sleep(0.05)

    def update_ui(self):
        filter_mode = self.filter_combo.currentText()
        filtered_items = []

        for ip, data in systems.items():
            is_active = data["latency"] is not None
            if filter_mode == "Active" and not is_active:
                continue
            if filter_mode == "Offline" and is_active:
                continue
            filtered_items.append((ip, data))

        self._suppress_item_change = True
        self.table.setRowCount(len(filtered_items))

        for row, (ip, data) in enumerate(filtered_items):
            latency = data["latency"]
            name = data["name"]
            status = "🟢 Active" if latency is not None else "🔴 Offline"

            self.table.setItem(row, 0, QTableWidgetItem(ip))
            name_item = QTableWidgetItem(name)
            name_item.setFlags(name_item.flags() | Qt.ItemIsEditable)
            self.table.setItem(row, 1, name_item)
            self.table.setItem(row, 2, QTableWidgetItem(f"{latency:.1f} ms" if latency else "Timeout"))
            self.table.setItem(row, 3, QTableWidgetItem(status))
        self._suppress_item_change = False

    def handle_item_change(self, item):
        if self._suppress_item_change:
            return
        row = item.row()
        col = item.column()
        if col != 1:
            return
        ip = self.table.item(row, 0).text()
        new_name = item.text().strip()
        systems[ip]["name"] = new_name
        ALIASES[ip] = new_name
        save_aliases(ALIASES)

    def export_filtered(self):
        path, _ = QFileDialog.getSaveFileName(self, "Save CSV", "filtered_export.csv", "CSV Files (*.csv)")
        if not path:
            return

        with open(path, "w", newline="") as f:
            writer = csv.writer(f)
            writer.writerow(["IP", "Name", "Latency (ms)", "Status"])

            for row in range(self.table.rowCount()):
                ip = self.table.item(row, 0).text()
                name = self.table.item(row, 1).text()
                latency = self.table.item(row, 2).text()
                status = self.table.item(row, 3).text()
                writer.writerow([ip, name, latency, status])

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = PingMonitor()
    window.resize(700, 600)
    window.show()
    sys.exit(app.exec_())
'''